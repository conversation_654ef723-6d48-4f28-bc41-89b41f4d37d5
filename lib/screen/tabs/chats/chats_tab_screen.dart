import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_cubit.dart';
import 'package:toii_mesh/cubit/xmtp/xmtp_state.dart';
import 'package:toii_mesh/model/chat/chat_models.dart';
import 'package:toii_mesh/screen/chat/chat_screen.dart';
import 'package:toii_mesh/screen/tabs/chats/widgets/chat_search_bar.dart';
import 'package:toii_mesh/screen/tabs/chats/widgets/conversation_list.dart';
import 'package:toii_mesh/screen/tabs/chats/widgets/empty_state.dart';
import 'package:toii_mesh/screen/tabs/chats/widgets/error_state.dart';
import 'package:toii_xmtp_flutter/toii_xmtp_flutter.dart';

class ChatsTabScreen extends StatefulWidget {
  const ChatsTabScreen({super.key});

  @override
  State<ChatsTabScreen> createState() => _ChatsTabScreenState();
}

class _ChatsTabScreenState extends State<ChatsTabScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Auto-initialize XMTP client when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeXmtpClient();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _initializeXmtpClient() async {
    final xmtpCubit = context.read<XmtpCubit>();
    
    // Only initialize if not already initialized
    if (!xmtpCubit.hasClient) {
      final canAutoInit = await xmtpCubit.canAutoInitialize();
      if (canAutoInit) {
        await xmtpCubit.autoInitializeClient();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Search bar
            ChatSearchBar(
              controller: _searchController,
              onAddPressed: _handleAddPressed,
              onQrPressed: _handleQrPressed,
              onSearchChanged: _handleSearchChanged,
            ),

            // Chat list
            Expanded(
              child: BlocBuilder<XmtpCubit, XmtpState>(
                builder: (context, state) {
                  if (state is XmtpInitial) {
                    return ChatEmptyState(
                      onInitializePressed: _initializeXmtpClient,
                    );
                  } else if (state is XmtpLoading) {
                    return const ChatEmptyState(isLoading: true);
                  } else if (state is XmtpConversationsLoaded) {
                    return _buildConversationsList(state.conversations);
                  } else if (state is XmtpError) {
                    return ChatErrorState(
                      message: state.message,
                      onRetryPressed: _initializeXmtpClient,
                    );
                  }
                  return const ChatEmptyState();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConversationsList(List<Conversation> conversations) {
    // Convert XMTP conversations to ChatConversation models
    final chatConversations = conversations
        .map((conv) => ChatConversation.fromConversation(conv))
        .toList();

    return ConversationList(
      conversations: chatConversations,
      onConversationTap: _handleConversationTap,
    );
  }

  void _handleConversationTap(ChatConversation conversation) {
    final xmtpCubit = context.read<XmtpCubit>();
    final currentUserInboxId = xmtpCubit.inboxId;

    if (currentUserInboxId != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ChatScreen(
            conversation: conversation,
            currentUserInboxId: currentUserInboxId,
          ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('XMTP client not properly initialized'),
        ),
      );
    }
  }

  void _handleAddPressed() {
    // TODO: Implement add new conversation functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add new conversation - Coming soon!'),
      ),
    );
  }

  void _handleQrPressed() {
    // TODO: Implement QR code scanner functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('QR Scanner - Coming soon!'),
      ),
    );
  }

  void _handleSearchChanged(String query) {
    // TODO: Implement search functionality
    // For now, just log the search query
    debugPrint('Search query: $query');
  }
}
